import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@dust-tt/sparkle";
import Link from "next/link";
import type { ReactElement } from "react";

import { Grid, H1, H2, H3, P } from "@app/components/home/<USER>";
import type { LandingLayoutProps } from "@app/components/home/<USER>";
import LandingLayout from "@app/components/home/<USER>";
import {
  getParticleShapeIndexByName,
  shapeNames,
} from "@app/components/home/<USER>";
import { classNames } from "@app/lib/utils";

export async function getStaticProps() {
  return {
    props: {
      shape: getParticleShapeIndexByName(shapeNames.icosahedron),
      gtmTrackingId: process.env.NEXT_PUBLIC_GTM_TRACKING_ID ?? null,
    },
  };
}

export default function Vulnerability() {
  return (
    <>
      <div className="container flex w-full flex-col gap-16 px-6 md:gap-24">
        <div
          className={classNames(
            "flex flex-col justify-end gap-4 pt-24",
            "col-span-10"
          )}
        >
          <H3 className="text-center text-muted-foreground">Security</H3>
          <div className="flex flex-row justify-center">
            <H1
              mono
              className="text-center text-5xl font-medium md:text-6xl lg:text-7xl"
            >
              Vulnerability Disclosure Program
            </H1>
          </div>
          <div className="flex flex-row justify-center pt-4">
            <Link
              href="https://hackerone.com/dust/reports/new?type=team&report_type=vulnerability"
              shallow={true}
            >
              <Button
                variant="highlight"
                size="md"
                label="Report a vulnerability"
                icon={ArrowRightIcon}
              />
            </Link>
          </div>
        </div>

        <Grid>
          <div
            className={classNames(
              "flex flex-col items-start gap-6",
              "col-span-10 col-start-2"
            )}
          >
            <H2>Report a Security Vulnerability</H2>
            <P>
              At Dust, our top priority is the safety, security, and control of
              our customers' data. To excel at this, we welcome the vital role
              that security researchers play in keeping systems and data safe.
              To encourage the responsible disclosure of potential security
              vulnerabilities, the Dust security team has committed to working
              with the community to verify, reproduce, and respond to legitimate
              reports through our HackerOne's{" "}
              <Link
                className="underline"
                href="https://hackerone.com/dust"
                target="_blank"
              >
                vulnerability disclosure program
              </Link>
              .
            </P>

            <P>
              If you believe you've identified a potential security
              vulnerability in any Dust service, please report it to us. We will
              investigate all legitimate reports and do our best to quickly
              respond and address reported issues.
            </P>

            <P>
              Note: our codebase is accessible at{" "}
              <Link
                href="https://github.com/dust-tt/dust"
                target="_blank"
                className="underline"
              >
                https://github.com/dust-tt.dust
              </Link>
            </P>
          </div>
        </Grid>

        <Grid>
          <div
            className={classNames(
              "flex flex-col items-start gap-6",
              "col-span-10 col-start-2"
            )}
          >
            <H2>Disclosure Policy</H2>
            <P>
              Please do not discuss any vulnerability (even resolved ones)
              outside of the program without express consent from us. Follow
              HackerOne's{" "}
              <Link
                className="underline"
                href="https://www.hackerone.com/disclosure-guidelines"
                target="_blank"
              >
                disclosure guidelines
              </Link>
              .
            </P>
          </div>
        </Grid>

        <Grid>
          <div
            className={classNames(
              "flex flex-col items-start gap-6",
              "col-span-10 col-start-2"
            )}
          >
            <H2>Program Rules</H2>
            <div className="flex flex-col gap-1">
              <P>
                &#x2022;&nbsp; Please provide detailed reports with reproducible
                steps. If the report is not detailed enough to reproduce the
                issue, the issue may not be marked as triaged.
              </P>
              <P>
                &#x2022;&nbsp; Submit one vulnerability per report unless you
                need to chain vulnerabilities to provide impact.
              </P>
              <P>
                &#x2022;&nbsp; When duplicates occur, we only triage the first
                report received (provided that it can be fully reproduced).
              </P>
              <P>
                &#x2022;&nbsp; Multiple vulnerabilities caused by one underlying
                issue will be treated as one valid report.
              </P>
              <P>
                &#x2022;&nbsp; Social engineering (e.g., phishing, vishing,
                smishing) is prohibited.
              </P>
              <P>
                &#x2022;&nbsp; Make a good faith effort to avoid privacy
                violations, destruction of data, and interruption or degradation
                of our service.
              </P>
              <P>
                &#x2022;&nbsp; Only interact with accounts you own or with
                explicit permission of the account holder.
              </P>
              <P>
                &#x2022;&nbsp; Researchers should add headers to requests such
                as <code>X-HackerOne-Research: [H1 username]</code>
              </P>
            </div>
          </div>
        </Grid>
      </div>
    </>
  );
}

Vulnerability.getLayout = (
  page: ReactElement,
  pageProps: LandingLayoutProps
) => {
  return <LandingLayout pageProps={pageProps}>{page}</LandingLayout>;
};

-- Migration created on Jun 12, 2024
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'agent_browse_configurations';
CREATE TABLE IF NOT EXISTS "agent_browse_configurations" ("id"  SERIAL , "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL, "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL, "sId" VARCHAR(255) NOT NULL, "name" VARCHAR(255), "description" TEXT, "agentConfigurationId" INTEGER NOT NULL REFERENCES "agent_configurations" ("id") ON DELETE CASCADE ON UPDATE CASCADE, PRIMARY KEY ("id"));
SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'agent_browse_configurations' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;
CREATE UNIQUE INDEX "agent_browse_configurations_s_id" ON "agent_browse_configurations" ("sId");
CREATE INDEX CONCURRENTLY "agent_browse_configurations_agent_configuration_id" ON "agent_browse_configurations" ("agentConfigurationId");
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'agent_browse_actions';
CREATE TABLE IF NOT EXISTS "agent_browse_actions" ("id"  SERIAL , "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL, "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL, "browseConfigurationId" VARCHAR(255) NOT NULL, "urls" TEXT[] NOT NULL, "output" JSONB, "functionCallId" VARCHAR(255), "functionCallName" VARCHAR(255), "step" INTEGER NOT NULL, "agentMessageId" INTEGER NOT NULL REFERENCES "agent_messages" ("id") ON DELETE NO ACTION ON UPDATE CASCADE, PRIMARY KEY ("id"));
SELECT i.relname AS name, ix.indisprimary AS primary, ix.indisunique AS unique, ix.indkey AS indkey, array_agg(a.attnum) as column_indexes, array_agg(a.attname) AS column_names, pg_get_indexdef(ix.indexrelid) AS definition FROM pg_class t, pg_class i, pg_index ix, pg_attribute a WHERE t.oid = ix.indrelid AND i.oid = ix.indexrelid AND a.attrelid = t.oid AND t.relkind = 'r' and t.relname = 'agent_browse_actions' GROUP BY i.relname, ix.indexrelid, ix.indisprimary, ix.indisunique, ix.indkey ORDER BY i.relname;
CREATE INDEX CONCURRENTLY "agent_browse_actions_agent_message_id" ON "agent_browse_actions" ("agentMessageId");

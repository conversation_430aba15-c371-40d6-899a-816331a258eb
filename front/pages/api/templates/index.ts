import type { NextApiRequest, NextApiResponse } from "next";

import { TemplateResource } from "@app/lib/resources/template_resource";
import { apiError, withLogging } from "@app/logger/withlogging";
import type { WithAPIErrorResponse } from "@app/types";

export type AssistantTemplateListType = ReturnType<
  TemplateResource["toListJSON"]
>;

export interface FetchAssistantTemplatesResponse {
  templates: AssistantTemplateListType[];
}

async function handler(
  req: NextApiRequest,
  res: NextApiResponse<WithAPIErrorResponse<FetchAssistantTemplatesResponse>>
): Promise<void> {
  switch (req.method) {
    case "GET":
      const templates = await TemplateResource.listAll({
        visibility: "published",
      });

      return res
        .status(200)
        .json({ templates: templates.map((t) => t.toListJSON()) });

    default:
      return apiError(req, res, {
        status_code: 405,
        api_error: {
          type: "method_not_supported_error",
          message: "The method passed is not supported, POST is expected.",
        },
      });
  }
}

export default withLogging(handler);
